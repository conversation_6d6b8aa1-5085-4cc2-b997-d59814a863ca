@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

:root {
  /* Professional Color Palette */
  --qx-primary: #0066cc;
  --qx-primary-light: #3385d6;
  --qx-primary-dark: #0052a3;
  --qx-primary-hover: #0059b8;
  --qx-primary-50: #e6f2ff;
  --qx-primary-100: #b3d9ff;
  --qx-primary-200: #80c0ff;

  /* Semantic Colors */
  --qx-success: #10b981;
  --qx-success-light: #34d399;
  --qx-success-dark: #059669;
  --qx-success-bg: #ecfdf5;

  --qx-error: #ef4444;
  --qx-error-light: #f87171;
  --qx-error-dark: #dc2626;
  --qx-error-bg: #fef2f2;

  --qx-warning: #f59e0b;
  --qx-warning-light: #fbbf24;
  --qx-warning-dark: #d97706;
  --qx-warning-bg: #fffbeb;

  --qx-info: #3b82f6;
  --qx-info-light: #60a5fa;
  --qx-info-dark: #2563eb;
  --qx-info-bg: #eff6ff;

  /* Neutral Colors */
  --qx-white: #ffffff;
  --qx-gray-50: #f9fafb;
  --qx-gray-100: #f3f4f6;
  --qx-gray-200: #e5e7eb;
  --qx-gray-300: #d1d5db;
  --qx-gray-400: #9ca3af;
  --qx-gray-500: #6b7280;
  --qx-gray-600: #4b5563;
  --qx-gray-700: #374151;
  --qx-gray-800: #1f2937;
  --qx-gray-900: #111827;

  /* Text Colors */
  --qx-text-primary: #111827;
  --qx-text-secondary: #374151;
  --qx-text-tertiary: #6b7280;
  --qx-text-disabled: #9ca3af;
  --qx-text-inverse: #ffffff;

  /* Background Colors */
  --qx-bg-primary: #ffffff;
  --qx-bg-secondary: #f9fafb;
  --qx-bg-tertiary: #f3f4f6;
  --qx-bg-overlay: rgba(17, 24, 39, 0.5);

  /* Border Colors */
  --qx-border-light: #f3f4f6;
  --qx-border-medium: #e5e7eb;
  --qx-border-dark: #d1d5db;
  --qx-border-focus: var(--qx-primary);

  /* Shadows */
  --qx-shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --qx-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --qx-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --qx-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --qx-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --qx-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --qx-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Gradients */
  --qx-gradient-primary: linear-gradient(
    135deg,
    var(--qx-primary) 0%,
    var(--qx-primary-dark) 100%
  );
  --qx-gradient-secondary: linear-gradient(
    135deg,
    var(--qx-gray-100) 0%,
    var(--qx-gray-200) 100%
  );
  --qx-gradient-success: linear-gradient(
    135deg,
    var(--qx-success) 0%,
    var(--qx-success-dark) 100%
  );

  /* Border Radius */
  --qx-radius-none: 0;
  --qx-radius-sm: 0.25rem;
  --qx-radius-md: 0.375rem;
  --qx-radius-lg: 0.5rem;
  --qx-radius-xl: 0.75rem;
  --qx-radius-2xl: 1rem;
  --qx-radius-full: 9999px;

  /* Spacing Scale */
  --qx-space-0: 0;
  --qx-space-1: 0.25rem;
  --qx-space-2: 0.5rem;
  --qx-space-3: 0.75rem;
  --qx-space-4: 1rem;
  --qx-space-5: 1.25rem;
  --qx-space-6: 1.5rem;
  --qx-space-8: 2rem;
  --qx-space-10: 2.5rem;
  --qx-space-12: 3rem;
  --qx-space-16: 4rem;
  --qx-space-20: 5rem;

  /* Typography Scale */
  --qx-font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  --qx-font-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;

  --qx-font-xs: 0.75rem;
  --qx-font-sm: 0.875rem;
  --qx-font-base: 1rem;
  --qx-font-lg: 1.125rem;
  --qx-font-xl: 1.25rem;
  --qx-font-2xl: 1.5rem;
  --qx-font-3xl: 1.875rem;
  --qx-font-4xl: 2.25rem;

  --qx-line-height-tight: 1.25;
  --qx-line-height-snug: 1.375;
  --qx-line-height-normal: 1.5;
  --qx-line-height-relaxed: 1.625;
  --qx-line-height-loose: 2;

  --qx-font-weight-light: 300;
  --qx-font-weight-normal: 400;
  --qx-font-weight-medium: 500;
  --qx-font-weight-semibold: 600;
  --qx-font-weight-bold: 700;

  /* Button Sizes */
  --qx-btn-xs: var(--qx-space-1) var(--qx-space-2);
  --qx-btn-sm: var(--qx-space-2) var(--qx-space-3);
  --qx-btn-md: var(--qx-space-3) var(--qx-space-4);
  --qx-btn-lg: var(--qx-space-4) var(--qx-space-6);
  --qx-btn-xl: var(--qx-space-5) var(--qx-space-8);

  /* Transitions */
  --qx-transition-fast: 150ms ease;
  --qx-transition-normal: 200ms ease;
  --qx-transition-slow: 300ms ease;

  /* Z-index Scale */
  --qx-z-dropdown: 1000;
  --qx-z-sticky: 1020;
  --qx-z-fixed: 1030;
  --qx-z-modal-backdrop: 1040;
  --qx-z-modal: 1050;
  --qx-z-popover: 1060;
  --qx-z-tooltip: 1070;
}

/* Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Modern scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--qx-gray-100);
  border-radius: var(--qx-radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--qx-gray-300);
  border-radius: var(--qx-radius-full);
  transition: background var(--qx-transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--qx-gray-400);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--qx-gray-300) var(--qx-gray-100);
}

/* Selection styling */
::selection {
  background: var(--qx-primary-100);
  color: var(--qx-primary-dark);
}

::-moz-selection {
  background: var(--qx-primary-100);
  color: var(--qx-primary-dark);
}

/* Main Container Styles */
.qx-wrap {
  max-width: 100%;
  padding: var(--qx-space-0);
  font-family: var(--qx-font-family);
  background-color: var(--qx-bg-secondary);
  min-height: 100vh;
  color: var(--qx-text-primary);
  line-height: var(--qx-line-height-normal);
}

.qx-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--qx-bg-primary);
  border-bottom: 1px solid var(--qx-border-medium);
  padding: var(--qx-space-6) var(--qx-space-8);
  box-shadow: var(--qx-shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--qx-z-sticky);
}

.qx-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--qx-gradient-primary);
  box-shadow: 0 2px 8px rgba(0, 102, 204, 0.3);
}

/* Modern glassmorphism effect for header */
.qx-header::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  pointer-events: none;
}

/* Header Styles */
.qx-wrap h1 {
  color: var(--qx-text-primary);
  font-size: var(--qx-font-2xl);
  font-weight: var(--qx-font-weight-bold);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--qx-space-3);
}

.qx-wrap h1::before {
  content: "⚡";
  font-size: var(--qx-font-xl);
  color: var(--qx-primary);
}

.qx-footer {
  display: flex;
  align-items: center;
  gap: var(--qx-space-2);
}

.qx-footer p {
  margin: 0;
  font-size: var(--qx-font-sm);
  color: var(--qx-text-tertiary);
  font-weight: var(--qx-font-weight-medium);
}

/* Content Container */
.qx-content {
  padding: var(--qx-space-8);
  max-width: 1200px;
  margin: 0 auto;
}

/* Manual Update Section */
.xero-manual-update {
  display: flex;
  gap: var(--qx-space-8);
  justify-content: space-between;
  align-items: flex-start;
  background: var(--qx-bg-primary);
  border: 1px solid var(--qx-border-medium);
  border-radius: var(--qx-radius-lg);
  padding: var(--qx-space-6);
  margin-bottom: var(--qx-space-8);
  box-shadow: var(--qx-shadow-sm);
  position: relative;
  overflow: hidden;
}

.xero-manual-update::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--qx-gradient-primary);
}

.xero-manual-update .description {
  flex: 1;
  margin: 0;
  color: var(--qx-text-secondary);
  font-size: var(--qx-font-sm);
  line-height: var(--qx-line-height-relaxed);
}

/* Button Styles */
.qx-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--qx-space-2);
  font-family: var(--qx-font-family);
  font-weight: var(--qx-font-weight-medium);
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--qx-radius-lg);
  cursor: pointer;
  transition: all var(--qx-transition-normal);
  position: relative;
  overflow: hidden;
}

.qx-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.qx-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Button Sizes */
.qx-btn-sm {
  padding: var(--qx-btn-sm);
  font-size: var(--qx-font-sm);
}

.qx-btn-md {
  padding: var(--qx-btn-md);
  font-size: var(--qx-font-base);
}

.qx-btn-lg {
  padding: var(--qx-btn-lg);
  font-size: var(--qx-font-lg);
}

/* Button Variants */
.qx-btn-primary {
  background: var(--qx-gradient-primary);
  color: var(--qx-text-inverse);
  border-color: var(--qx-primary);
}

.qx-btn-primary:hover:not(:disabled) {
  background: var(--qx-primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--qx-shadow-lg);
}

.qx-btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--qx-shadow-sm);
}

.qx-btn-secondary {
  background: var(--qx-bg-primary);
  color: var(--qx-text-primary);
  border-color: var(--qx-border-dark);
}

.qx-btn-secondary:hover:not(:disabled) {
  background: var(--qx-bg-tertiary);
  border-color: var(--qx-border-focus);
}

.qx-btn-danger {
  background: var(--qx-gradient-success);
  background: linear-gradient(
    135deg,
    var(--qx-error) 0%,
    var(--qx-error-dark) 100%
  );
  color: var(--qx-text-inverse);
  border-color: var(--qx-error);
}

.qx-btn-danger:hover:not(:disabled) {
  background: var(--qx-error-dark);
  transform: translateY(-1px);
  box-shadow: var(--qx-shadow-md);
}

/* Legacy Button Mappings */
#xero-manual-update-button,
.qx-add-field-mapping,
.xero-add-card,
.submit-button,
.qx-add-scope {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--qx-space-2);
  font-family: var(--qx-font-family);
  font-weight: var(--qx-font-weight-medium);
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--qx-radius-lg);
  cursor: pointer;
  transition: all var(--qx-transition-normal);
  position: relative;
  overflow: hidden;
  padding: var(--qx-btn-md);
  font-size: var(--qx-font-base);
  background: var(--qx-gradient-primary);
  color: var(--qx-text-inverse);
  border-color: var(--qx-primary);
}

#xero-manual-update-button:hover:not(:disabled),
.qx-add-field-mapping:hover:not(:disabled),
.xero-add-card:hover:not(:disabled),
.submit-button:hover:not(:disabled),
.qx-add-scope:hover:not(:disabled) {
  background: var(--qx-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--qx-shadow-md);
}

#xero-manual-update-button:focus,
.qx-add-field-mapping:focus,
.xero-add-card:focus,
.submit-button:focus,
.qx-add-scope:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.qx-remove-scope,
.qx-remove-card,
.qx-remove-mapping {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--qx-space-2);
  font-family: var(--qx-font-family);
  font-weight: var(--qx-font-weight-medium);
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--qx-radius-lg);
  cursor: pointer;
  transition: all var(--qx-transition-normal);
  position: relative;
  overflow: hidden;
  padding: var(--qx-btn-sm);
  font-size: var(--qx-font-sm);
  background: linear-gradient(
    135deg,
    var(--qx-error) 0%,
    var(--qx-error-dark) 100%
  );
  color: var(--qx-text-inverse);
  border-color: var(--qx-error);
  margin: var(--qx-space-4);
}

.qx-remove-scope:hover:not(:disabled),
.qx-remove-card:hover:not(:disabled),
.qx-remove-mapping:hover:not(:disabled) {
  background: var(--qx-error-dark);
  transform: translateY(-1px);
  box-shadow: var(--qx-shadow-md);
}

.qx-remove-scope:focus,
.qx-remove-card:focus,
.qx-remove-mapping:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Alert Styles */
.alert {
  border-radius: var(--qx-radius-lg);
  margin: var(--qx-space-4) 0;
  padding: var(--qx-space-4) var(--qx-space-5);
  border: 1px solid transparent;
  display: flex;
  align-items: flex-start;
  gap: var(--qx-space-3);
  font-size: var(--qx-font-sm);
  line-height: var(--qx-line-height-relaxed);
  position: relative;
  overflow: hidden;
}

.alert::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.alert-success {
  background-color: var(--qx-success-bg);
  border-color: var(--qx-success-light);
  color: var(--qx-success-dark);
}

.alert-success::before {
  background-color: var(--qx-success);
}

.alert-error {
  background-color: var(--qx-error-bg);
  border-color: var(--qx-error-light);
  color: var(--qx-error-dark);
}

.alert-error::before {
  background-color: var(--qx-error);
}

.alert-warning {
  background-color: var(--qx-warning-bg);
  border-color: var(--qx-warning-light);
  color: var(--qx-warning-dark);
}

.alert-warning::before {
  background-color: var(--qx-warning);
}

.alert-info {
  background-color: var(--qx-info-bg);
  border-color: var(--qx-info-light);
  color: var(--qx-info-dark);
}

.alert-info::before {
  background-color: var(--qx-info);
}

/* Responsive Design - Mobile First Approach */

/* Mobile Styles (default) */
.qx-wrap {
  padding: var(--qx-space-4);
}

.qx-header {
  padding: var(--qx-space-4) var(--qx-space-4);
  flex-direction: column;
  gap: var(--qx-space-3);
  text-align: center;
}

.qx-header h1 {
  font-size: var(--qx-font-xl);
}

.xero-manual-update {
  flex-direction: column;
  gap: var(--qx-space-4);
  text-align: center;
}

.nav-tab-wrapper {
  padding: 0 var(--qx-space-4);
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.nav-tab {
  white-space: nowrap;
  min-width: max-content;
}

.tab-content {
  padding: var(--qx-space-4);
}

.form-row {
  grid-template-columns: 1fr;
  gap: var(--qx-space-4);
}

.form-mapping-card {
  margin-bottom: var(--qx-space-4);
}

.card-header {
  padding: var(--qx-space-4);
}

.form-mapping-fields {
  padding: var(--qx-space-4);
}

.form-section {
  margin-bottom: var(--qx-space-6);
}

/* Touch-friendly buttons */
.qx-btn {
  min-height: 44px;
  min-width: 44px;
}

/* Modal adjustments for mobile */
.xero-modal-content {
  width: 95%;
  margin: 2vh auto;
  max-height: 96vh;
  overflow-y: auto;
}

.xero-modal-header {
  padding: var(--qx-space-4) var(--qx-space-5);
}

.xero-modal-body {
  padding: var(--qx-space-5);
}

/* Tablet Styles */
@media screen and (min-width: 768px) {
  .qx-wrap {
    padding: var(--qx-space-6);
  }

  .qx-header {
    padding: var(--qx-space-6) var(--qx-space-8);
    flex-direction: row;
    text-align: left;
  }

  .qx-header h1 {
    font-size: var(--qx-font-2xl);
  }

  .xero-manual-update {
    flex-direction: row;
    gap: var(--qx-space-8);
    text-align: left;
  }

  .nav-tab-wrapper {
    padding: 0 var(--qx-space-8);
  }

  .tab-content {
    padding: var(--qx-space-6);
  }

  .form-row {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--qx-space-5);
  }

  .card-header {
    padding: var(--qx-space-5) var(--qx-space-6);
  }

  .form-mapping-fields {
    padding: var(--qx-space-6);
  }

  .xero-modal-content {
    width: 85%;
    margin: 5vh auto;
  }

  .xero-modal-header {
    padding: var(--qx-space-6) var(--qx-space-8);
  }

  .xero-modal-body {
    padding: var(--qx-space-8);
  }
}

/* Desktop Styles */
@media screen and (min-width: 1024px) {
  .qx-wrap {
    padding: var(--qx-space-8);
  }

  .form-row {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .xero-modal-content {
    width: 80%;
    max-width: 600px;
  }
}

/* Large Desktop Styles */
@media screen and (min-width: 1200px) {
  .qx-content {
    max-width: 1200px;
  }

  .form-row {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
}

/* Legacy WordPress Admin Responsive Support */
@media screen and (max-width: 782px) {
  .form-table th {
    width: 100%;
    display: block;
    padding-bottom: 0;
  }

  .form-table td {
    padding-left: 0;
    padding-right: 0;
  }

  .form-group {
    min-width: 100%;
  }

  /* Stack form elements vertically on mobile */
  .form-table th,
  .form-table td {
    display: block;
    width: 100%;
  }

  .form-table th {
    padding-bottom: var(--qx-space-2);
  }

  .form-table td {
    padding-top: 0;
    padding-bottom: var(--qx-space-4);
  }
}

/* High DPI / Retina Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .qx-loading-spinner {
    border-width: 1px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark Mode Support (if needed in future) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here if needed */
}

/* Professional Visual Enhancements */

/* Status Indicators */
.qx-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--qx-space-2);
  padding: var(--qx-space-1) var(--qx-space-3);
  border-radius: var(--qx-radius-full);
  font-size: var(--qx-font-xs);
  font-weight: var(--qx-font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.qx-status-indicator::before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: var(--qx-radius-full);
  flex-shrink: 0;
}

.qx-status-success {
  background: var(--qx-success-bg);
  color: var(--qx-success-dark);
}

.qx-status-success::before {
  background: var(--qx-success);
}

.qx-status-error {
  background: var(--qx-error-bg);
  color: var(--qx-error-dark);
}

.qx-status-error::before {
  background: var(--qx-error);
}

.qx-status-warning {
  background: var(--qx-warning-bg);
  color: var(--qx-warning-dark);
}

.qx-status-warning::before {
  background: var(--qx-warning);
}

.qx-status-info {
  background: var(--qx-info-bg);
  color: var(--qx-info-dark);
}

.qx-status-info::before {
  background: var(--qx-info);
}

/* Icon Enhancements */
.qx-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
  font-size: inherit;
}

.qx-icon-sm {
  font-size: var(--qx-font-sm);
}

.qx-icon-lg {
  font-size: var(--qx-font-lg);
}

.qx-icon-xl {
  font-size: var(--qx-font-xl);
}

/* Badge Component */
.qx-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--qx-space-1) var(--qx-space-2);
  border-radius: var(--qx-radius-md);
  font-size: var(--qx-font-xs);
  font-weight: var(--qx-font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.qx-badge-primary {
  background: var(--qx-primary-50);
  color: var(--qx-primary-dark);
}

.qx-badge-success {
  background: var(--qx-success-bg);
  color: var(--qx-success-dark);
}

.qx-badge-error {
  background: var(--qx-error-bg);
  color: var(--qx-error-dark);
}

/* Tooltip Component */
.qx-tooltip {
  position: relative;
  cursor: help;
}

.qx-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--qx-gray-800);
  color: var(--qx-white);
  padding: var(--qx-space-2) var(--qx-space-3);
  border-radius: var(--qx-radius-md);
  font-size: var(--qx-font-xs);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--qx-transition-normal);
  z-index: var(--qx-z-tooltip);
  margin-bottom: var(--qx-space-2);
}

.qx-tooltip::before {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--qx-gray-800);
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--qx-transition-normal);
  z-index: var(--qx-z-tooltip);
}

.qx-tooltip:hover::after,
.qx-tooltip:hover::before {
  opacity: 1;
}

/* Utility Classes */
.qx-text-center {
  text-align: center;
}
.qx-text-left {
  text-align: left;
}
.qx-text-right {
  text-align: right;
}

.qx-font-light {
  font-weight: var(--qx-font-weight-light);
}
.qx-font-normal {
  font-weight: var(--qx-font-weight-normal);
}
.qx-font-medium {
  font-weight: var(--qx-font-weight-medium);
}
.qx-font-semibold {
  font-weight: var(--qx-font-weight-semibold);
}
.qx-font-bold {
  font-weight: var(--qx-font-weight-bold);
}

.qx-text-xs {
  font-size: var(--qx-font-xs);
}
.qx-text-sm {
  font-size: var(--qx-font-sm);
}
.qx-text-base {
  font-size: var(--qx-font-base);
}
.qx-text-lg {
  font-size: var(--qx-font-lg);
}
.qx-text-xl {
  font-size: var(--qx-font-xl);
}

.qx-text-primary {
  color: var(--qx-text-primary);
}
.qx-text-secondary {
  color: var(--qx-text-secondary);
}
.qx-text-tertiary {
  color: var(--qx-text-tertiary);
}
.qx-text-success {
  color: var(--qx-success);
}
.qx-text-error {
  color: var(--qx-error);
}
.qx-text-warning {
  color: var(--qx-warning);
}

.qx-bg-primary {
  background-color: var(--qx-bg-primary);
}
.qx-bg-secondary {
  background-color: var(--qx-bg-secondary);
}
.qx-bg-tertiary {
  background-color: var(--qx-bg-tertiary);
}

.qx-shadow-sm {
  box-shadow: var(--qx-shadow-sm);
}
.qx-shadow-md {
  box-shadow: var(--qx-shadow-md);
}
.qx-shadow-lg {
  box-shadow: var(--qx-shadow-lg);
}

.qx-rounded-sm {
  border-radius: var(--qx-radius-sm);
}
.qx-rounded-md {
  border-radius: var(--qx-radius-md);
}
.qx-rounded-lg {
  border-radius: var(--qx-radius-lg);
}
.qx-rounded-xl {
  border-radius: var(--qx-radius-xl);
}
.qx-rounded-full {
  border-radius: var(--qx-radius-full);
}

.qx-border {
  border: 1px solid var(--qx-border-medium);
}
.qx-border-light {
  border: 1px solid var(--qx-border-light);
}
.qx-border-dark {
  border: 1px solid var(--qx-border-dark);
}

/* Spacing Utilities */
.qx-m-0 {
  margin: 0;
}
.qx-m-1 {
  margin: var(--qx-space-1);
}
.qx-m-2 {
  margin: var(--qx-space-2);
}
.qx-m-3 {
  margin: var(--qx-space-3);
}
.qx-m-4 {
  margin: var(--qx-space-4);
}

.qx-p-0 {
  padding: 0;
}
.qx-p-1 {
  padding: var(--qx-space-1);
}
.qx-p-2 {
  padding: var(--qx-space-2);
}
.qx-p-3 {
  padding: var(--qx-space-3);
}
.qx-p-4 {
  padding: var(--qx-space-4);
}

.qx-mb-0 {
  margin-bottom: 0;
}
.qx-mb-1 {
  margin-bottom: var(--qx-space-1);
}
.qx-mb-2 {
  margin-bottom: var(--qx-space-2);
}
.qx-mb-3 {
  margin-bottom: var(--qx-space-3);
}
.qx-mb-4 {
  margin-bottom: var(--qx-space-4);
}

.qx-mt-0 {
  margin-top: 0;
}
.qx-mt-1 {
  margin-top: var(--qx-space-1);
}
.qx-mt-2 {
  margin-top: var(--qx-space-2);
}
.qx-mt-3 {
  margin-top: var(--qx-space-3);
}
.qx-mt-4 {
  margin-top: var(--qx-space-4);
}

/* Modern Visual Enhancements */

/* Floating Action Button Style */
.qx-fab {
  position: fixed;
  bottom: var(--qx-space-6);
  right: var(--qx-space-6);
  width: 56px;
  height: 56px;
  border-radius: var(--qx-radius-full);
  background: var(--qx-gradient-primary);
  color: var(--qx-text-inverse);
  border: none;
  box-shadow: var(--qx-shadow-lg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--qx-font-xl);
  transition: all var(--qx-transition-normal);
  z-index: var(--qx-z-fixed);
}

.qx-fab:hover {
  transform: scale(1.1);
  box-shadow: var(--qx-shadow-xl);
}

/* Modern Card with Subtle Animation */
.qx-modern-card {
  background: var(--qx-bg-primary);
  border-radius: var(--qx-radius-2xl);
  padding: var(--qx-space-6);
  box-shadow: var(--qx-shadow-sm);
  border: 1px solid var(--qx-border-light);
  transition: all var(--qx-transition-normal);
  position: relative;
  overflow: hidden;
}

.qx-modern-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.6s;
}

.qx-modern-card:hover::before {
  left: 100%;
}

.qx-modern-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--qx-shadow-lg);
}

/* Gradient Text Effect */
.qx-gradient-text {
  background: var(--qx-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: var(--qx-font-weight-bold);
}

/* Modern Toggle Switch */
.qx-toggle {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 28px;
}

.qx-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.qx-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--qx-gray-300);
  transition: var(--qx-transition-normal);
  border-radius: var(--qx-radius-full);
}

.qx-toggle-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: var(--qx-white);
  transition: var(--qx-transition-normal);
  border-radius: var(--qx-radius-full);
  box-shadow: var(--qx-shadow-sm);
}

.qx-toggle input:checked + .qx-toggle-slider {
  background: var(--qx-gradient-primary);
}

.qx-toggle input:checked + .qx-toggle-slider:before {
  transform: translateX(24px);
}

/* Modern Notification Badge */
.qx-notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--qx-error);
  color: var(--qx-white);
  border-radius: var(--qx-radius-full);
  padding: var(--qx-space-1) var(--qx-space-2);
  font-size: var(--qx-font-xs);
  font-weight: var(--qx-font-weight-bold);
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: qx-pulse 2s infinite;
}

/* Modern Divider */
.qx-divider {
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    var(--qx-border-medium),
    transparent
  );
  margin: var(--qx-space-6) 0;
}

.qx-divider-vertical {
  width: 1px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent,
    var(--qx-border-medium),
    transparent
  );
  margin: 0 var(--qx-space-4);
}

/* Form Control Base Styles */
.qx-form-control {
  display: block;
  width: 100%;
  font-family: var(--qx-font-family);
  font-size: var(--qx-font-base);
  font-weight: var(--qx-font-weight-normal);
  line-height: var(--qx-line-height-normal);
  color: var(--qx-text-primary);
  background-color: var(--qx-bg-primary);
  background-clip: padding-box;
  border: 1px solid var(--qx-border-dark);
  border-radius: var(--qx-radius-lg);
  transition: border-color var(--qx-transition-normal),
    box-shadow var(--qx-transition-normal);
  padding: var(--qx-space-3) var(--qx-space-4);
}

.qx-form-control:focus {
  border-color: var(--qx-border-focus);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.qx-form-control:disabled,
.qx-form-control[readonly] {
  background-color: var(--qx-bg-tertiary);
  opacity: 1;
  color: var(--qx-text-disabled);
  cursor: not-allowed;
}

.qx-form-control::placeholder {
  color: var(--qx-text-tertiary);
  opacity: 1;
}

/* Input Styles */
input[type="text"],
input[type="password"],
input[type="email"],
input[type="url"],
input[type="number"],
input[type="tel"],
input[type="search"] {
  display: block;
  width: 100%;
  max-width: 400px;
  font-family: var(--qx-font-family);
  font-size: var(--qx-font-base);
  font-weight: var(--qx-font-weight-normal);
  line-height: var(--qx-line-height-normal);
  color: var(--qx-text-primary);
  background-color: var(--qx-bg-primary);
  background-clip: padding-box;
  border: 1px solid var(--qx-border-dark);
  border-radius: var(--qx-radius-lg);
  transition: border-color var(--qx-transition-normal),
    box-shadow var(--qx-transition-normal);
  padding: var(--qx-space-3) var(--qx-space-4);
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="search"]:focus {
  border-color: var(--qx-border-focus);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Modern input hover states */
input[type="text"]:hover:not(:focus),
input[type="password"]:hover:not(:focus),
input[type="email"]:hover:not(:focus),
input[type="url"]:hover:not(:focus),
input[type="number"]:hover:not(:focus),
input[type="tel"]:hover:not(:focus),
input[type="search"]:hover:not(:focus) {
  border-color: var(--qx-gray-400);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Textarea Styles */
textarea {
  display: block;
  width: 100%;
  font-family: var(--qx-font-family);
  font-size: var(--qx-font-base);
  font-weight: var(--qx-font-weight-normal);
  line-height: var(--qx-line-height-normal);
  color: var(--qx-text-primary);
  background-color: var(--qx-bg-primary);
  background-clip: padding-box;
  border: 1px solid var(--qx-border-dark);
  border-radius: var(--qx-radius-lg);
  transition: border-color var(--qx-transition-normal),
    box-shadow var(--qx-transition-normal);
  padding: var(--qx-space-3) var(--qx-space-4);
  resize: vertical;
  min-height: 100px;
}

textarea:focus {
  border-color: var(--qx-border-focus);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

/* Select Styles */
select {
  display: block;
  width: 100%;
  max-width: 400px;
  font-family: var(--qx-font-family);
  font-size: var(--qx-font-base);
  font-weight: var(--qx-font-weight-normal);
  line-height: var(--qx-line-height-normal);
  color: var(--qx-text-primary);
  background-color: var(--qx-bg-primary);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--qx-space-3) center;
  background-size: 16px 12px;
  border: 1px solid var(--qx-border-dark);
  border-radius: var(--qx-radius-lg);
  transition: border-color var(--qx-transition-normal),
    box-shadow var(--qx-transition-normal);
  padding: var(--qx-space-3) var(--qx-space-10) var(--qx-space-3)
    var(--qx-space-4);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

select:focus {
  border-color: var(--qx-border-focus);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

select:disabled {
  background-color: var(--qx-bg-tertiary);
  color: var(--qx-text-disabled);
  cursor: not-allowed;
}

/* Description Text */
.description {
  color: var(--qx-text-tertiary);
  font-size: var(--qx-font-sm);
  margin-top: var(--qx-space-2);
  line-height: var(--qx-line-height-relaxed);
}

/* Form Mapping Card Styles */
.form-mapping-card {
  background: var(--qx-bg-primary);
  border: 1px solid var(--qx-border-medium);
  border-radius: var(--qx-radius-xl);
  margin-bottom: var(--qx-space-6);
  box-shadow: var(--qx-shadow-sm);
  transition: all var(--qx-transition-normal);
  overflow: hidden;
  position: relative;
}

.form-mapping-card:hover {
  box-shadow: var(--qx-shadow-lg);
  transform: translateY(-4px);
}

.form-mapping-card:hover::before {
  box-shadow: 0 4px 12px rgba(0, 102, 204, 0.4);
}

.form-mapping-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--qx-gradient-primary);
}

.card-header {
  background: var(--qx-bg-secondary);
  border-bottom: 1px solid var(--qx-border-medium);
  padding: var(--qx-space-5) var(--qx-space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.card-header h4 {
  margin: 0;
  color: var(--qx-text-primary);
  font-size: var(--qx-font-lg);
  font-weight: var(--qx-font-weight-semibold);
  display: flex;
  align-items: center;
  gap: var(--qx-space-2);
}

.card-header h4::before {
  content: "📋";
  font-size: var(--qx-font-base);
}

.toggle-card {
  background: none;
  border: none;
  color: var(--qx-text-secondary);
  cursor: pointer;
  padding: var(--qx-space-2);
  border-radius: var(--qx-radius-full);
  transition: all var(--qx-transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-card:hover {
  background: var(--qx-bg-tertiary);
  color: var(--qx-text-primary);
}

.form-mapping-fields {
  padding: var(--qx-space-6);
}

.form-section {
  margin-bottom: var(--qx-space-8);
  padding-bottom: var(--qx-space-6);
  border-bottom: 1px solid var(--qx-border-light);
  position: relative;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h5 {
  color: var(--qx-text-primary);
  font-size: var(--qx-font-base);
  font-weight: var(--qx-font-weight-semibold);
  margin: 0 0 var(--qx-space-4) 0;
  display: flex;
  align-items: center;
  gap: var(--qx-space-2);
}

.form-section h5::before {
  content: "⚙️";
  font-size: var(--qx-font-sm);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--qx-space-5);
  margin-bottom: var(--qx-space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  display: block;
  margin-bottom: var(--qx-space-2);
  font-weight: var(--qx-font-weight-medium);
  color: var(--qx-text-secondary);
  font-size: var(--qx-font-sm);
}

/* Card Footer */
.card-footer {
  background: var(--qx-bg-secondary);
  border-top: 1px solid var(--qx-border-medium);
  padding: var(--qx-space-4) var(--qx-space-6);
  display: flex;
  justify-content: flex-end;
  gap: var(--qx-space-3);
}

/* Field Mapping Styles */
.field-mapping-container {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.field-mapping-table {
  width: 100%;
  margin-bottom: 15px;
}

.field-mapping-header {
  display: flex;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px 4px 0 0;
  font-weight: 600;
}

.field-mapping-row {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #eee;
  align-items: center;
  transition: background-color 0.2s;
}

.field-mapping-row:hover {
  background-color: #f9f9f9;
}

.field-mapping-column {
  flex: 1;
  padding: 0 10px;
}

.field-mapping-column.actions {
  flex: 0 0 80px;
  text-align: right;
}

.field-mapping-column input,
.field-mapping-column select {
  width: 100%;
  max-width: none;
}

.field-mapping-help {
  margin-top: 10px;
  font-style: italic;
  color: #666;
}

/* Toggle Card Animation */
.form-mapping-fields {
  transition: all 0.3s ease;
}

/* Loading Spinner Animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Sync Button Styles */
.xero-sync-button,
.xero-continue-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 150px;
}

.xero-sync-button .button-content,
.xero-continue-button .button-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.xero-sync-button.loading,
.xero-continue-button.loading {
  background-color: var(--qx-primary-dark);
}

/* Scope Items Styling */
.scope-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  animation: fadeIn 0.3s ease;
}

.scope-item input {
  flex: 1;
}

.qx-add-scope {
  margin: 10px;
}

/* Tab Content Animation */
.tab-content {
  animation: fadeIn 0.3s ease;
}

/* Custom Switch Styling */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2271b1;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Tab Navigation Styles */
.nav-tab-wrapper {
  background: var(--qx-bg-primary);
  border-bottom: 1px solid var(--qx-border-medium);
  margin-bottom: var(--qx-space-8);
  padding: 0 var(--qx-space-8);
  box-shadow: var(--qx-shadow-sm);
  position: sticky;
  top: 73px; /* Adjust based on header height */
  z-index: var(--qx-z-sticky);
  display: flex;
  gap: var(--qx-space-1);
}

.nav-tab {
  background: transparent;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--qx-text-secondary);
  font-size: var(--qx-font-sm);
  font-weight: var(--qx-font-weight-medium);
  padding: var(--qx-space-4) var(--qx-space-6);
  transition: all var(--qx-transition-normal);
  text-decoration: none;
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--qx-space-2);
  border-radius: var(--qx-radius-lg) var(--qx-radius-lg) 0 0;
}

.nav-tab:hover {
  background: var(--qx-bg-tertiary);
  color: var(--qx-text-primary);
  transform: translateY(-1px);
}

.nav-tab.nav-tab-active {
  background: var(--qx-bg-secondary);
  color: var(--qx-primary);
  border-bottom-color: var(--qx-primary);
  font-weight: var(--qx-font-weight-semibold);
}

.nav-tab .dashicons {
  font-size: var(--qx-font-base);
  width: var(--qx-font-base);
  height: var(--qx-font-base);
}

/* Tab Content */
.tab-content {
  background: var(--qx-bg-primary);
  border-radius: var(--qx-radius-lg);
  padding: var(--qx-space-8);
  margin-bottom: var(--qx-space-8);
  box-shadow: var(--qx-shadow-sm);
  border: 1px solid var(--qx-border-medium);
}

/* Form Styles */
.form-table {
  margin-top: 20px;
}

.form-table th {
  padding: 20px 10px 20px 0;
  width: 200px;
  font-weight: 600;
}

.form-table td {
  padding: 20px 10px;
}

/* Animation Styles */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Error Styles */
.error {
  border-color: #dc3232 !important;
}

/* Import Progress Styles */
.xero-modal-header.import-complete {
  background-color: #1e7e34;
}

.xero-modal-header.import-complete h3 {
  color: #fff;
}

.progress-container {
  margin-bottom: 20px;
}

.progress-bar {
  height: 20px;
  background-color: #f1f1f1;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--qx-primary);
  width: 0%;
  transition: width 0.5s ease;
}

.progress-bar .progress-bar-fill.complete {
  background-color: #28a745;
  transition: background-color 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #666;
}

.import-details {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;
}

.modal-actions {
  margin-top: 15px;
  text-align: right;
}

.cancel-import-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-import-button:hover {
  background-color: #d32f2f;
}

.rate-limit-warning {
  background-color: #fff3cd;
  color: #856404;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;
  border-left: 4px solid #ffc107;
}

.rate-limited {
  display: inline-block;
  background-color: #f44336;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.rate-normal {
  display: inline-block;
  background-color: #4caf50;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.rate-limit-timer {
  font-weight: bold;
  color: #856404;
  margin-top: 5px;
}

.alert-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  color: #856404;
}

/* Modal Styles - Professional Design */
.xero-modal {
  display: none;
  position: fixed;
  z-index: var(--qx-z-modal-backdrop);
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: var(--qx-bg-overlay);
  animation: qx-modal-backdrop-appear var(--qx-transition-slow) ease-out;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

@keyframes qx-modal-backdrop-appear {
  from {
    background-color: rgba(17, 24, 39, 0);
    backdrop-filter: blur(0px);
  }
  to {
    background-color: var(--qx-bg-overlay);
    backdrop-filter: blur(4px);
  }
}

.xero-modal-content {
  background-color: var(--qx-bg-primary);
  margin: 5vh auto;
  border: none;
  width: 90%;
  max-width: 600px;
  border-radius: var(--qx-radius-2xl);
  box-shadow: var(--qx-shadow-2xl);
  animation: qx-modal-content-appear var(--qx-transition-slow) ease-out;
  overflow: hidden;
  position: relative;
}

@keyframes qx-modal-content-appear {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.xero-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--qx-gradient-secondary);
  padding: var(--qx-space-6) var(--qx-space-8);
  border-bottom: 1px solid var(--qx-border-medium);
  position: relative;
}

.xero-modal-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--qx-gradient-primary);
}

.xero-modal-header h3 {
  margin: 0;
  color: var(--qx-text-primary);
  font-size: var(--qx-font-xl);
  font-weight: var(--qx-font-weight-semibold);
  display: flex;
  align-items: center;
  gap: var(--qx-space-3);
}

.xero-modal-close {
  background: none;
  border: none;
  color: var(--qx-text-tertiary);
  font-size: var(--qx-font-2xl);
  font-weight: var(--qx-font-weight-normal);
  cursor: pointer;
  padding: var(--qx-space-2);
  border-radius: var(--qx-radius-full);
  transition: all var(--qx-transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.xero-modal-close:hover,
.xero-modal-close:focus {
  background: var(--qx-bg-tertiary);
  color: var(--qx-text-primary);
  transform: scale(1.1);
}

.xero-modal-body {
  padding: var(--qx-space-8);
}

/* Professional Loading States */
.qx-loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--qx-border-medium);
  border-radius: var(--qx-radius-full);
  border-top-color: var(--qx-primary);
  animation: qx-spin 1s linear infinite;
}

.qx-loading-spinner-lg {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

@keyframes qx-spin {
  to {
    transform: rotate(360deg);
  }
}

/* Button Loading States */
.qx-btn.loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
}

.qx-btn.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: var(--qx-radius-full);
  animation: qx-spin 1s linear infinite;
  color: var(--qx-text-inverse);
}

/* Progress Bar Enhancements */
.qx-progress {
  background-color: var(--qx-bg-tertiary);
  border-radius: var(--qx-radius-full);
  height: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: var(--qx-shadow-inner);
}

.qx-progress-bar {
  background: var(--qx-gradient-primary);
  height: 100%;
  width: 0%;
  transition: width 0.6s ease;
  position: relative;
  border-radius: var(--qx-radius-full);
}

.qx-progress-bar.animated::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: qx-progress-shimmer 2s infinite;
}

@keyframes qx-progress-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Micro-interactions */
.qx-hover-lift {
  transition: transform var(--qx-transition-normal),
    box-shadow var(--qx-transition-normal);
}

.qx-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--qx-shadow-lg);
}

.qx-hover-scale {
  transition: transform var(--qx-transition-fast);
}

.qx-hover-scale:hover {
  transform: scale(1.05);
}

/* Fade In Animation */
.qx-fade-in {
  animation: qx-fade-in 0.5s ease-out;
}

@keyframes qx-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide In Animation */
.qx-slide-in {
  animation: qx-slide-in 0.4s ease-out;
}

@keyframes qx-slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse Animation for Important Elements */
.qx-pulse {
  animation: qx-pulse 2s infinite;
}

@keyframes qx-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Skeleton Loading */
.qx-skeleton {
  background: linear-gradient(
    90deg,
    var(--qx-bg-tertiary) 25%,
    var(--qx-bg-secondary) 50%,
    var(--qx-bg-tertiary) 75%
  );
  background-size: 200% 100%;
  animation: qx-skeleton-loading 1.5s infinite;
  border-radius: var(--qx-radius-md);
}

@keyframes qx-skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Confirmation Dialog Styles */
.confirmation-message {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1.5;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-sync {
  background-color: var(--qx-primary);
  color: white;
}

.confirm-sync:hover {
  background-color: var(--qx-primary-dark);
}

.cancel-sync {
  background-color: #f1f1f1;
  color: #333;
}

.cancel-sync:hover {
  background-color: #e1e1e1;
}

/* Sync Button Styles */

@keyframes xero-spin {
  to {
    transform: rotate(360deg);
  }
}

/* Modal Styles */
.xero-modal {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  animation: modal-bg-appear 0.3s ease-out;
  backdrop-filter: blur(2px);
}

@keyframes modal-bg-appear {
  from {
    background-color: rgba(0, 0, 0, 0);
  }

  to {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.xero-modal-content {
  background-color: #ffffff;
  margin: 10% auto;
  border: none;
  width: 80%;
  max-width: 500px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  contain: content;
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.xero-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f4f5f7;
  padding: 15px 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #dee2e6;
}

.xero-modal-header h3 {
  margin: 0;
  color: #333333;
  font-size: 20px;
  font-weight: 500;
}

.xero-modal-body {
  padding: 15px 20px;
}

/* Progress Bar Styles */
.progress-bar {
  background-color: #f0f0f0;
  border-radius: 4px;
  height: 20px;
  overflow: hidden;
  position: relative;
}

.progress-bar-fill {
  background-color: #2271b1;
  height: 100%;
  width: 0%;
  transition: width 0.3s ease;
  position: relative;
}

.progress-bar-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* Button Styles */
.xero-modal button.button {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Cancel button styles */
.xero-cancel-import,
.xero-cancel-confirm {
  background-color: #f1f1f1 !important;
  color: #333 !important;
  border-color: #ccc !important;
  border-radius: 8px;
  padding: 8px 16px;
}

.xero-cancel-import:hover,
.xero-cancel-confirm:hover {
  background-color: #e2e2e2 !important;
  border-color: #999 !important;
}

/* Confirm/Start button styles */
.xero-confirm-sync {
  background-color: #2271b1 !important;
  color: white !important;
  border-color: #2271b1 !important;
  border-radius: 4px;
  font-weight: 600;
}

.xero-confirm-sync:hover {
  background-color: #135e96 !important;
  border-color: #135e96 !important;
}

/* Cancel import button (red) */
.xero-cancel-import {
  background-color: #dc3545 !important;
  color: white !important;
  border-color: #dc3545 !important;
}

.xero-cancel-import:hover {
  background-color: #c82333 !important;
  border-color: #bd2130 !important;
}

/* Processing state styles */
.processing .progress-bar-fill {
  transition: none;
  animation: progress-animation 2s linear infinite;
  background-image: linear-gradient(
    -45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 50px 50px;
}

@keyframes progress-animation {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 50px 50px;
  }
}

/* Completed state styles */
.progress-bar-fill.complete {
  background-color: #28a745;
}

.xero-modal-header.import-complete {
  background-color: #28a745;
  color: white;
}

/* Sync Buttons Container */
.xero-sync-buttons {
  display: flex;
  gap: 10px;
}

/* Import Progress Styles */
.xero-import-progress {
  margin-bottom: 15px;
}

.progress-text {
  margin-top: 5px;
  font-size: 13px;
}

/* Rate Limit Alert Styles */
#rate-limit-info {
  margin-bottom: 15px;
  display: none;
}

.rate-limit-alert {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
}

.rate-limit-alert h4 {
  margin-top: 0;
  margin-bottom: 5px;
}

.rate-limit-message {
  margin-bottom: 5px;
}

.rate-limit-timer {
  font-weight: bold;
}
